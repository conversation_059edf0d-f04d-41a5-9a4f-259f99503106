package com.jurassic.myhealth.service.auth.util

import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class ResultExtensionsTest {

    @Test
    fun `flatMap should transform success value`() {
        // Given
        val result = Result.success(5)
        
        // When
        val transformed = result.andThen { value ->
            Result.success(value * 2) 
        }
        
        // Then
        assertTrue(transformed.isSuccess)
        assertEquals(10, transformed.getOrNull())
    }
    
    @Test
    fun `flatMap should propagate failure`() {
        // Given
        val exception = RuntimeException("Test exception")
        val result = Result.failure<Int>(exception)
        
        // When
        val transformed = result.andThen { value ->
            Result.success(value * 2) 
        }
        
        // Then
        assertTrue(transformed.isFailure)
        assertEquals(exception, transformed.exceptionOrNull())
    }
    
    @Test
    fun `flatMap should handle transformation failure`() {
        // Given
        val result = Result.success(5)
        val exception = RuntimeException("Transform exception")
        
        // When
        val transformed = result.andThen {
            Result.failure<Int>(exception) 
        }
        
        // Then
        assertTrue(transformed.isFailure)
        assertEquals(exception, transformed.exceptionOrNull())
    }
    
    @Test
    fun `flatMapSuspend should transform success value`() = runBlocking {
        // Given
        val result = Result.success(5)
        
        // When
        val transformed = result.andThenSuspend { value ->
            Result.success(value * 2) 
        }
        
        // Then
        assertTrue(transformed.isSuccess)
        assertEquals(10, transformed.getOrNull())
    }
    
    @Test
    fun `flatMapSuspend should propagate failure`() = runBlocking {
        // Given
        val exception = RuntimeException("Test exception")
        val result = Result.failure<Int>(exception)
        
        // When
        val transformed = result.andThenSuspend { value ->
            Result.success(value * 2) 
        }
        
        // Then
        assertTrue(transformed.isFailure)
        assertEquals(exception, transformed.exceptionOrNull())
    }
    
    @Test
    fun `flatMapSuspend should handle transformation failure`() = runBlocking {
        // Given
        val result = Result.success(5)
        val exception = RuntimeException("Transform exception")
        
        // When
        val transformed = result.andThenSuspend {
            Result.failure<Int>(exception) 
        }
        
        // Then
        assertTrue(transformed.isFailure)
        assertEquals(exception, transformed.exceptionOrNull())
    }
}
