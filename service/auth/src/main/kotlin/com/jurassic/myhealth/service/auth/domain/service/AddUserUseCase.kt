package com.jurassic.myhealth.service.auth.domain.service

import com.jurassic.myhealth.service.auth.domain.gateway.TokenValidatorGateway
import com.jurassic.myhealth.service.auth.domain.model.User
import com.jurassic.myhealth.service.auth.domain.model.UserId
import com.jurassic.myhealth.service.auth.domain.repository.UserRepository
import com.jurassic.myhealth.service.auth.util.andThen
import org.springframework.stereotype.Service
import java.time.Instant
import java.util.*

@Service
class AddUserUseCase(
    private val userRepository: UserRepository,
    private val tokenValidator: TokenValidatorGateway,
    private val tokenExtractorUseCase: TokenExtractorUseCase,
) {

    suspend fun execute(
        firstName: String,
        lastName: String,
        email: String,
        authHeader: String
    ): Result<User> =
        tokenExtractorUseCase.execute(authHeader)
            .andThen {
                tokenValidator.validate(it)
            }.map {
                User(
                    id = UserId(UUID.randomUUID().toString()),
                    externalId = it.externalId,
                    identityProvider = it.identityProvider,
                    email = email,
                    firstName = firstName,
                    lastName = lastName,
                    createdAt = Instant.now(),
                    updatedAt = Instant.now()
                )
            }.andThen { user ->
                userRepository.save(user)
            }
}