package com.jurassic.myhealth.service.auth.api.config

import com.jurassic.myhealth.service.auth.domain.service.AuthenticateUseCase
import com.jurassic.myhealth.service.auth.domain.service.TokenExtractorUseCase
import kotlinx.coroutines.reactor.mono
import mu.KotlinLogging
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.HttpStatus
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.security.config.annotation.method.configuration.EnableReactiveMethodSecurity
import org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity
import org.springframework.security.config.web.server.SecurityWebFiltersOrder
import org.springframework.security.config.web.server.ServerHttpSecurity
import org.springframework.security.core.context.ReactiveSecurityContextHolder
import org.springframework.security.web.server.SecurityWebFilterChain
import org.springframework.security.web.server.context.NoOpServerSecurityContextRepository
import org.springframework.web.server.WebFilter
import reactor.core.publisher.Mono

private val logger = KotlinLogging.logger {}

@Configuration
@EnableWebFluxSecurity
@EnableReactiveMethodSecurity
class SecurityConfig(
    private val authenticateUseCase: AuthenticateUseCase,
    private val tokenExtractorUseCase: TokenExtractorUseCase,
) {

    private fun isPublicEndpoint(path: String): Boolean = when {
        path == "/api/v1/users/register" -> true
        path == "/api/v1/sample/public" -> true
        path == "/api/v1/auth-sample/public" -> true
        path.startsWith("/actuator/") -> true
        path == "/favicon.ico" -> true
        path.startsWith("/static/") -> true
        path.startsWith("/css/") -> true
        path.startsWith("/js/") -> true
        path.startsWith("/images/") -> true
        else -> false
    }

    @Bean
    fun authenticationWebFilter(): WebFilter = WebFilter { exchange, chain ->
        val path = exchange.request.path.value()

        if (isPublicEndpoint(path)) {
            logger.debug { "Skipping authentication for public endpoint: $path" }
            return@WebFilter chain.filter(exchange)
        }

        val authHeader = exchange.request.headers.getFirst("Authorization")
        if (authHeader == null) {
            logger.debug { "No Authorization header for path: $path" }
            return@WebFilter chain.filter(exchange)
        }

        tokenExtractorUseCase.execute(authHeader)
            .fold(
                onSuccess = { token ->
                    mono {
                        authenticateUseCase.execute(token)
                    }.flatMap { result ->
                        result.fold(
                            onSuccess = { userPrincipal ->
                                logger.debug { "User authenticated: ${userPrincipal.internalUserId}" }
                                val authentication = UsernamePasswordAuthenticationToken(userPrincipal, token, emptyList())
                                chain.filter(exchange)
                                    .contextWrite(ReactiveSecurityContextHolder.withAuthentication(authentication))
                            },
                            onFailure = { error ->
                                logger.warn { "Token validation failed for path $path: ${error.message}" }
                                chain.filter(exchange)
                            }
                        )
                    }
                },
                onFailure = { e ->
                    if (path.startsWith("/api/")) {
                        logger.error("Authentication error for path: $path - ${e.message}", e)
                    } else {
                        logger.debug("Authentication skipped for non-API path: $path - ${e.message}")
                    }
                    chain.filter(exchange)
                }
            )
    }

    @Bean
    fun securityWebFilterChain(http: ServerHttpSecurity): SecurityWebFilterChain =
        http
            .cors { it.disable() }
            .csrf { it.disable() }
            .httpBasic { it.disable() }
            .formLogin { it.disable() }
            .logout { it.disable() }
            .securityContextRepository(NoOpServerSecurityContextRepository.getInstance())
            .authorizeExchange { exchanges ->
                exchanges
                    // Public endpoints
                    .pathMatchers("/api/v1/users/register").permitAll()
                    .pathMatchers("/api/v1/sample/public").permitAll()
                    .pathMatchers("/api/v1/auth-sample/public").permitAll()
                    .pathMatchers("/api/v1/sync/luxmed").permitAll() // Temporary for testing
                    .pathMatchers("/actuator/**").permitAll()
                    // Common static resources
                    .pathMatchers("/favicon.ico").permitAll()
                    .pathMatchers("/static/**").permitAll()
                    .pathMatchers("/css/**").permitAll()
                    .pathMatchers("/js/**").permitAll()
                    .pathMatchers("/images/**").permitAll()
                    // Protected endpoints
                    .pathMatchers("/api/v1/**").authenticated()
                    .anyExchange().authenticated()
            }
            .addFilterBefore(authenticationWebFilter(), SecurityWebFiltersOrder.AUTHENTICATION)
            .exceptionHandling { exceptions ->
                exceptions.authenticationEntryPoint { exchange, ex ->
                    val path = exchange.request.path.value()

                    if (!isPublicEndpoint(path)) {
                        logger.warn { "Authentication failure for path: $path, message: ${ex.message}" }
                    }

                    val response = exchange.response
                    response.statusCode = HttpStatus.UNAUTHORIZED
                    response.headers.add("Content-Type", "application/json")

                    val body = "{\"error\":\"Unauthorized\", \"message\":\"Authentication is required to access this resource\", \"path\":\"$path\"}"
                    val buffer = response.bufferFactory().wrap(body.toByteArray())
                    response.writeWith(Mono.just(buffer))
                }
                exceptions.accessDeniedHandler { exchange, denied ->
                    val path = exchange.request.path.value()
                    logger.warn { "Access denied for path: $path, message: ${denied.message}" }

                    val response = exchange.response
                    response.statusCode = HttpStatus.FORBIDDEN
                    response.headers.add("Content-Type", "application/json")

                    val body = "{\"error\":\"Forbidden\", \"message\":\"You don't have permission to access this resource\", \"path\":\"$path\"}"
                    val buffer = response.bufferFactory().wrap(body.toByteArray())
                    response.writeWith(Mono.just(buffer))
                }
            }
            .build()
}
