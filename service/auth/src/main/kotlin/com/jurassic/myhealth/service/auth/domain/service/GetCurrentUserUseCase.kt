package com.jurassic.myhealth.service.auth.domain.service

import com.jurassic.myhealth.service.auth.domain.model.error.AuthError
import com.jurassic.myhealth.service.auth.domain.model.User
import com.jurassic.myhealth.service.auth.domain.model.UserId
import com.jurassic.myhealth.service.auth.domain.repository.UserRepository
import org.springframework.stereotype.Service

@Service
class GetCurrentUserUseCase(private val userRepository: UserRepository) {

    internal suspend fun execute(userId: String): Result<User> =
        userRepository.get(UserId(userId))
}
