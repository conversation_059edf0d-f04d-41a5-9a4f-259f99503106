package com.jurassic.myhealth.service.auth.data.repository

import com.jurassic.myhealth.service.auth.data.persistant.entity.UserEntity
import com.jurassic.myhealth.service.auth.data.mapper.toDomain
import com.jurassic.myhealth.service.auth.data.mapper.toEntity
import com.jurassic.myhealth.service.auth.domain.model.error.AuthError
import com.jurassic.myhealth.service.auth.domain.model.ExternalId
import com.jurassic.myhealth.service.auth.domain.model.IdentityProviderType
import com.jurassic.myhealth.service.auth.domain.model.User
import com.jurassic.myhealth.service.auth.domain.model.UserId
import com.jurassic.myhealth.service.auth.domain.repository.UserRepository
import mu.KotlinLogging
import org.springframework.data.mongodb.core.ReactiveMongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.stereotype.Repository
import kotlinx.coroutines.reactive.awaitFirstOrNull

private val logger = KotlinLogging.logger {}

@Repository
class UserRepositoryImpl(
    private val reactiveMongoTemplate: ReactiveMongoTemplate
) : UserRepository {

    override suspend fun get(
        externalId: ExternalId,
        identityProvider: IdentityProviderType
    ): Result<User> =
        try {
            val query = Query.query(
                Criteria
                    .where("externalId").`is`(externalId.value)
                    .and("identityProvider").`is`(identityProvider.value)
            )

            val userEntity = reactiveMongoTemplate.findOne(query, UserEntity::class.java).awaitFirstOrNull()

            if (userEntity != null) {
                Result.success(userEntity.toDomain())
            } else {
                Result.failure(AuthError.UserNotFound(externalId.value))
            }
        } catch (e: Exception) {
            logger.error(e) { "Error finding user by external ID: ${externalId.value}" }
            Result.failure(AuthError.UserNotFound(externalId.value, "Error finding user: ${e.message}"))
        }

    override suspend fun get(id: UserId): Result<User> =
        try {
            val userEntity = reactiveMongoTemplate.findById(id.value, UserEntity::class.java).awaitFirstOrNull()

            if (userEntity != null) {
                Result.success(userEntity.toDomain())
            } else {
                Result.failure(AuthError.UserNotFound(id.value))
            }
        } catch (e: Exception) {
            logger.error(e) { "Error finding user by ID: ${id.value}" }
            Result.failure(AuthError.UserNotFound(id.value, "Error finding user: ${e.message}"))
        }

    override suspend fun save(user: User): Result<User> =
        try {
            val userEntity = user.toEntity()
            val savedEntity = reactiveMongoTemplate.save(userEntity).awaitFirstOrNull()
                ?: throw IllegalStateException("Failed to save user entity")
            Result.success(savedEntity.toDomain())
        } catch (e: Exception) {
            logger.error(e) { "Error saving user: ${user.id.value}" }
            Result.failure(AuthError.UserCreationError("Error saving user: ${e.message}", e))
        }
}
