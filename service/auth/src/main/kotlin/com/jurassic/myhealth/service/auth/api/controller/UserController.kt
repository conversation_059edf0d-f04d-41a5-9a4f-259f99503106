package com.jurassic.myhealth.service.auth.api.controller

import com.jurassic.myhealth.service.auth.api.dto.ApiErrorResponse
import com.jurassic.myhealth.service.auth.api.dto.UserRequest
import com.jurassic.myhealth.service.auth.api.mapper.toApiErrorResponse
import com.jurassic.myhealth.service.auth.api.mapper.toDto
import com.jurassic.myhealth.service.auth.domain.model.AuthenticatedUserPrincipal
import com.jurassic.myhealth.service.auth.domain.service.AddUserUseCase
import com.jurassic.myhealth.service.auth.domain.service.GetCurrentUserUseCase
import kotlinx.coroutines.runBlocking
import mu.KotlinLogging
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.security.core.Authentication
import org.springframework.web.bind.annotation.*

private val logger = KotlinLogging.logger {}

@RestController
@RequestMapping("/api/v1/users")
class UserController(
    private val getCurrentUserUseCase: GetCurrentUserUseCase,
    private val addUserUseCase: AddUserUseCase,
) {

    @PostMapping("/register", produces = [MediaType.APPLICATION_JSON_VALUE])
    suspend fun registerUser(
        @RequestHeader("Authorization") authHeader: String,
        @RequestBody userRequest: UserRequest
    ): ResponseEntity<*> {
        try {
            return addUserUseCase.execute(
                authHeader = authHeader,
                email = userRequest.email,
                firstName = userRequest.firstName,
                lastName = userRequest.lastName,
            )
                .map { user ->
                    ResponseEntity.status(HttpStatus.CREATED).body(user.toDto())
                }
                .getOrElse { error -> error.toApiErrorResponse() }
        } catch (e: Exception) {
            logger.error("Error registering user", e)
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR.value(), "An unexpected error occurred"))
        }
    }

    @GetMapping("/me", produces = [MediaType.APPLICATION_JSON_VALUE])
    suspend fun getCurrentUserProfile(authentication: Authentication): ResponseEntity<*> {
        try {
            val principal = authentication.principal

            if (principal !is AuthenticatedUserPrincipal) {
                logger.warn { "Invalid principal type: ${principal?.javaClass?.name}" }
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiErrorResponse(HttpStatus.UNAUTHORIZED.value(), "Authentication required"))
            }

            val result = getCurrentUserUseCase.execute(principal.internalUserId)

            return result.map { user ->
                logger.info { "Getting profile for user: ${user.id.value}" }
                ResponseEntity.ok(user.toDto())
            }.getOrElse { error -> error.toApiErrorResponse() }
        } catch (e: Exception) {
            logger.error("Error getting user profile", e)
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR.value(), "An unexpected error occurred"))
        }
    }
}
