package com.jurassic.myhealth.service.auth.util

inline fun <T, R> Result<T>.andThen(transform: (T) -> Result<R>): Result<R> =
    fold(
        onSuccess = { transform(it) },
        onFailure = { Result.failure(it) }
    )

suspend inline fun <T, R> Result<T>.andThenSuspend(crossinline transform: suspend (T) -> Result<R>): Result<R> =
    fold(
        onSuccess = { transform(it) },
        onFailure = { Result.failure(it) }
    )