package com.jurassic.myhealth.service.healthcare.data.gateway.mapper

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.laboratory.CommentRemote
import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.laboratory.ExaminationRemote
import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.laboratory.LabResultResponse
import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.laboratory.LabResultStatusRemote
import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.laboratory.TestResultRemote
import com.jurassic.myhealth.service.healthcare.domain.model.SourceSystem
import com.jurassic.myhealth.service.healthcare.domain.model.UserId
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull

class LuxMedLabResultMapperTest {
    
    private val objectMapper: ObjectMapper = jacksonObjectMapper()
    private val mapper = LuxMedLabResultMapper(objectMapper)
    
    @Test
    fun `should map TestResultRemote to LabParameter correctly`() = runTest {
        // Given
        val userId = UserId("test-user")
        val testResults = listOf(
            TestResultRemote(
                name = "Hemoglobin",
                value = "14.5",
                measurement = "g/dL",
                minScopeFormatted = "12.0",
                maxScopeFormatted = "15.5",
                status = LabResultStatusRemote.NORMAL.id
            ),
            TestResultRemote(
                name = "Glucose",
                value = "110",
                measurement = "mg/dL",
                minScopeFormatted = "70",
                maxScopeFormatted = "100",
                status = LabResultStatusRemote.ABNORMAL.id
            ),
            TestResultRemote(
                name = "Information Entry",
                value = "This is just information",
                measurement = "",
                minScopeFormatted = null,
                maxScopeFormatted = null,
                status = LabResultStatusRemote.INFORMATION.id
            )
        )
        
        val examination = ExaminationRemote(
            results = testResults,
            name = "Complete Blood Count",
            comment = CommentRemote(comment = "Test completed successfully")
        )
        
        val labResultResponse = LabResultResponse(
            examinations = listOf(examination),
            eventId = 12345L,
            date = "2024-01-15T10:30:00",
            title = "Laboratory Test",
            status = 1
        )
        
        // When
        val labResult = mapper.mapLabResult(userId, labResultResponse)
        
        // Then
        assertNotNull(labResult)
        assertEquals("12345", labResult.externalId)
        assertEquals(SourceSystem.LUXMED, labResult.sourceSystem)
        assertEquals("Complete Blood Count", labResult.testName)
        assertEquals("Test completed successfully", labResult.notes)
        assertEquals("Completed", labResult.status)
        assertEquals(LocalDateTime.of(2024, 1, 15, 10, 30, 0), labResult.observationDate)
        
        // Should have 2 parameters (excluding information entry)
        assertEquals(2, labResult.parameters.size)
        
        // Check Hemoglobin parameter
        val hemoglobinParam = labResult.parameters.find { it.name == "Hemoglobin" }
        assertNotNull(hemoglobinParam)
        assertEquals("14.5", hemoglobinParam.value)
        assertEquals("g/dL", hemoglobinParam.unit)
        assertEquals("12.0", hemoglobinParam.min)
        assertEquals("15.5", hemoglobinParam.max)
        assertEquals(labResult.observationDate, hemoglobinParam.date)
        assertEquals("Normal", hemoglobinParam.interpretation)

        // Check Glucose parameter
        val glucoseParam = labResult.parameters.find { it.name == "Glucose" }
        assertNotNull(glucoseParam)
        assertEquals("110", glucoseParam.value)
        assertEquals("mg/dL", glucoseParam.unit)
        assertEquals("70", glucoseParam.min)
        assertEquals("100", glucoseParam.max)
        assertEquals(labResult.observationDate, glucoseParam.date)
        assertEquals("Abnormal", glucoseParam.interpretation)
    }
    
    @Test
    fun `should handle empty test results`() = runTest {
        // Given
        val userId = UserId("test-user")
        val labResultResponse = LabResultResponse(
            examinations = emptyList(),
            eventId = 12345L,
            date = "2024-01-15T10:30:00",
            title = "Laboratory Test",
            status = 1
        )
        
        // When
        val labResult = mapper.mapLabResult(userId, labResultResponse)
        
        // Then
        assertNotNull(labResult)
        assertEquals(0, labResult.parameters.size)
        assertEquals("Laboratory Test", labResult.testName)
    }
    
    @Test
    fun `should handle invalid date format`() = runTest {
        // Given
        val userId = UserId("test-user")
        val labResultResponse = LabResultResponse(
            examinations = emptyList(),
            eventId = 12345L,
            date = "invalid-date",
            title = "Laboratory Test",
            status = 1
        )
        
        // When
        val labResult = mapper.mapLabResult(userId, labResultResponse)
        
        // Then
        assertNull(labResult) // Should return null for invalid date
    }
    
    @Test
    fun `should handle critical test results`() = runTest {
        // Given
        val userId = UserId("test-user")
        val testResult = TestResultRemote(
            name = "Troponin",
            value = "50.0",
            measurement = "ng/mL",
            minScopeFormatted = "0",
            maxScopeFormatted = "0.04",
            status = LabResultStatusRemote.CRITICAL.id
        )
        
        val examination = ExaminationRemote(
            results = listOf(testResult),
            name = "Cardiac Markers"
        )
        
        val labResultResponse = LabResultResponse(
            examinations = listOf(examination),
            eventId = 12345L,
            date = "2024-01-15T10:30:00",
            title = "Laboratory Test",
            status = 1
        )
        
        // When
        val labResult = mapper.mapLabResult(userId, labResultResponse)
        
        // Then
        assertNotNull(labResult)
        assertEquals(1, labResult.parameters.size)
        
        val troponinParam = labResult.parameters.first()
        assertEquals("Troponin", troponinParam.name)
        assertEquals("50.0", troponinParam.value)
        assertEquals("ng/mL", troponinParam.unit)
        assertEquals("0", troponinParam.min)
        assertEquals("0.04", troponinParam.max)
        assertEquals(labResult.observationDate, troponinParam.date)
        assertEquals("Critical", troponinParam.interpretation)
    }
}
