package com.jurassic.myhealth.service.healthcare.domain.model.luxmed

/**
 * Domain object representing LuxMed authentication context
 * Contains all essential components required for successful LuxMed Patient Portal API calls
 */
data class LuxMedAuthentication(
    /**
     * JWT token for authorization-token header
     */
    val jwtToken: String,

    /**
     * ASP.NET session identifier cookie
     */
    val aspNetSessionId: String,

    /**
     * LuxMed authentication token cookie (must match JWT lx_token field)
     */
    val lxToken: String,

    /**
     * Session refresh token cookie
     */
    val refreshToken: String,

    /**
     * User permissions and session data JWT cookie
     */
    val userAdditionalInfo: String,

    /**
     * CSRF protection token
     */
    val xsrfToken: String,

    /**
     * Optional: Incapsula bot protection session
     */
    val incapsulaSessionId: String? = null,

    /**
     * Optional: Device identification
     */
    val deviceId: String? = null
) {
    init {
        require(jwtToken.isNotBlank()) { "JWT token cannot be blank" }
        require(aspNetSessionId.isNotBlank()) { "ASP.NET session ID cannot be blank" }
        require(lxToken.isNotBlank()) { "LX token cannot be blank" }
        require(refreshToken.isNotBlank()) { "Refresh token cannot be blank" }
        require(userAdditionalInfo.isNotBlank()) { "User additional info cannot be blank" }
        require(xsrfToken.isNotBlank()) { "XSRF token cannot be blank" }
    }
}