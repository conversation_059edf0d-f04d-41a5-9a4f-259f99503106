package com.jurassic.myhealth.service.healthcare.api.config

import org.springframework.context.annotation.Configuration
import org.springframework.web.reactive.config.WebFluxConfigurer
import org.springframework.web.reactive.result.method.annotation.ArgumentResolverConfigurer

@Configuration
class WebFluxConfig(
    private val authenticationArgumentResolver: AuthenticationArgumentResolver
) : WebFluxConfigurer {
    
    override fun configureArgumentResolvers(configurer: ArgumentResolverConfigurer) {
        configurer.addCustomResolver(authenticationArgumentResolver)
    }
}
