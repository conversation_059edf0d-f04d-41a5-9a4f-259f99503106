package com.jurassic.myhealth.service.healthcare.api.mapper

import com.jurassic.myhealth.service.healthcare.api.model.VisitDto
import com.jurassic.myhealth.service.healthcare.domain.model.Visit
import org.springframework.stereotype.Component

/**
 * Mapper between Visit domain model and VisitDto
 */
@Component
class VisitDtoMapper {
    
    fun toDto(visit: Visit): VisitDto {
        return VisitDto(
            id = visit.id,
            externalId = visit.externalId,
            sourceSystem = visit.sourceSystem.name,
            visitDate = visit.visitDate,
            doctorName = visit.doctorName,
            specialization = visit.specialization,
            facility = visit.facility,
            reason = visit.reason,
            recommendations = visit.recommendations,
            status = visit.status.name,
            visitType = visit.visitType,
            notes = visit.notes,
            createdAt = visit.createdAt,
            updatedAt = visit.updatedAt
        )
    }
    
    fun toDtoList(visits: List<Visit>): List<VisitDto> {
        return visits.map { toDto(it) }
    }
}
