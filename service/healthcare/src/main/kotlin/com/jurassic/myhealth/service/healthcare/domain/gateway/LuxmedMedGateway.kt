package com.jurassic.myhealth.service.healthcare.domain.gateway

import com.jurassic.myhealth.service.healthcare.domain.model.LabResult
import com.jurassic.myhealth.service.healthcare.domain.model.luxmed.LuxMedAuthentication
import com.jurassic.myhealth.service.healthcare.domain.model.UserId
import com.jurassic.myhealth.service.healthcare.domain.model.Visit

interface LuxmedMedGateway {

    suspend fun fetchAllResults(userId: UserId, luxmedAuthentication: LuxMedAuthentication): Result<LuxmedMedicalData>
}

/**
 * Container for all medical data fetched from LuxMed
 */
data class LuxmedMedicalData(
    /**
     * List of lab results fetched from LuxMed
     */
    val labResults: List<LabResult>,
    
    /**
     * List of visits fetched from LuxMed
     */
    val visits: List<Visit>
)
