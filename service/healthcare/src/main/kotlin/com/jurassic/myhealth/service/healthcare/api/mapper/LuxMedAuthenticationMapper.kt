package com.jurassic.myhealth.service.healthcare.api.mapper

import com.jurassic.myhealth.service.healthcare.api.model.LuxmedSyncRequest
import com.jurassic.myhealth.service.healthcare.domain.model.luxmed.LuxMedAuthentication
import org.springframework.stereotype.Component

@Component
class LuxMedAuthenticationMapper {

    fun toDomain(request: LuxmedSyncRequest): LuxMedAuthentication =
        LuxMedAuthentication(
            jwtToken = request.jwtToken?.takeIf { it.isNotBlank() }
                ?: throw IllegalArgumentException("JWT token cannot be blank"),
            aspNetSessionId = request.aspNetSessionId?.takeIf { it.isNotBlank() }
                ?: throw IllegalArgumentException("ASP.NET session ID cannot be blank"),
            lxToken = request.lxToken?.takeIf { it.isNotBlank() }
                ?: throw IllegalArgumentException("LX token cannot be blank"),
            refreshToken = request.refreshToken?.takeIf { it.isNotBlank() }
                ?: throw IllegalArgumentException("Refresh token cannot be blank"),
            userAdditionalInfo = request.userAdditionalInfo?.takeIf { it.isNotBlank() }
                ?: throw IllegalArgumentException("User additional info cannot be blank"),
            xsrfToken = request.xsrfToken?.takeIf { it.isNotBlank() }
                ?: throw IllegalArgumentException("XSRF token cannot be blank"),
            incapsulaSessionId = request.incapsulaSessionId?.takeIf { it.isNotBlank() },
            deviceId = request.deviceId?.takeIf { it.isNotBlank() }
        )
}
