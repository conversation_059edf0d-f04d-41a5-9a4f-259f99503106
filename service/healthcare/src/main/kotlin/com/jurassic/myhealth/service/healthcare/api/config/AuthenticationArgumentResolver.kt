package com.jurassic.myhealth.service.healthcare.api.config

import com.jurassic.myhealth.service.auth.domain.model.AuthenticatedUserPrincipal
import org.springframework.core.MethodParameter
import org.springframework.security.core.context.ReactiveSecurityContextHolder
import org.springframework.stereotype.Component
import org.springframework.web.reactive.BindingContext
import org.springframework.web.reactive.result.method.HandlerMethodArgumentResolver
import org.springframework.web.server.ServerWebExchange
import reactor.core.publisher.Mono

@Component
class AuthenticationArgumentResolver : HandlerMethodArgumentResolver {

    override fun supportsParameter(parameter: MethodParameter): <PERSON><PERSON><PERSON> {
        return parameter.parameterType == AuthenticatedUserPrincipal::class.java
    }

    override fun resolveArgument(
        parameter: MethodParameter,
        bindingContext: BindingContext,
        exchange: ServerWebExchange
    ): Mono<Any> {
        return ReactiveSecurityContextHolder.getContext()
            .map { securityContext ->
                val authentication = securityContext.authentication
                if (authentication?.principal != null) {
                    val userPrincipal = authentication.principal
                    when (userPrincipal) {
                        is AuthenticatedUserPrincipal -> userPrincipal
                        is String -> AuthenticatedUserPrincipal(userPrincipal)
                        else -> AuthenticatedUserPrincipal(userPrincipal.toString())
                    }
                } else {
                    AuthenticatedUserPrincipal("anonymous")
                }
            }
            .cast(Any::class.java)
    }
}
