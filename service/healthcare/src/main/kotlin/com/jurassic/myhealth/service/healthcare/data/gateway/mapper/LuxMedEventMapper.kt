package com.jurassic.myhealth.service.healthcare.data.gateway.mapper

import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.timeline.LuxMedEventTypeRemote
import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.timeline.TimelineEventRemote
import com.jurassic.myhealth.service.healthcare.domain.model.*
import mu.KotlinLogging
import org.springframework.stereotype.Component
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

private val logger = KotlinLogging.logger {}

/**
 * Mapper for converting LuxMed timeline events to domain models
 */
@Component
class LuxMedEventMapper {
    
    private val dateTimeFormatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME
    
    /**
     * Convert LuxMed timeline events to domain models
     */
    fun mapEvents(userId: UserId, events: List<TimelineEventRemote>): Pair<List<LabResult>, List<Visit>> {
        val labResults = mutableListOf<LabResult>()
        val visits = mutableListOf<Visit>()
        
        events.forEach { event ->
            try {
                when (LuxMedEventTypeRemote.fromId(event.eventType)) {
                    LuxMedEventTypeRemote.LABORATORY_TEST -> {
                        mapToLabResult(userId, event)?.let { labResults.add(it) }
                    }
                    LuxMedEventTypeRemote.VISIT,
                    LuxMedEventTypeRemote.TELEMEDICINE_VISIT,
                    LuxMedEventTypeRemote.PHONE_CONSULTATION,
                    LuxMedEventTypeRemote.EXAMINATION -> {
                        mapToVisit(userId, event)?.let { visits.add(it) }
                    }
                    else -> {
                        logger.debug { "Skipping event type ${event.eventType} for event ${event.eventId}" }
                    }
                }
            } catch (e: Exception) {
                logger.warn(e) { "Failed to map event ${event.eventId} of type ${event.eventType}" }
            }
        }
        
        return Pair(labResults, visits)
    }
    
    private fun mapToLabResult(userId: UserId, event: TimelineEventRemote): LabResult? {
        if (event.eventId == 0L) return null
        
        val observationDate = parseDateTime(event.date) ?: return null
        
        // For lab results, we create a basic structure
        // Detailed parameters would need to be fetched from event details
        val parameters = event.shortExaminationNames?.map { exam ->
            LabParameter(
                name = exam.name,
                value = "Pending", // Would be populated from detailed results
                unit = null,
                min = null,
                max = null,
                date = observationDate,
                interpretation = null
            )
        } ?: emptyList()
        
        return LabResult(
            userId = userId,
            externalId = event.eventId.toString(),
            sourceSystem = SourceSystem.LUXMED,
            testName = event.title.ifBlank { "Laboratory Test" },
            parameters = parameters,
            observationDate = observationDate,
            status = mapEventStatus(event.status),
            reportUrl = null,
            notes = null
        )
    }
    
    private fun mapToVisit(userId: UserId, event: TimelineEventRemote): Visit? {
        if (event.eventId == 0L) return null
        
        val visitDate = parseDateTime(event.date) ?: return null
        
        val doctorName = event.doctor?.let { doctor ->
            listOfNotNull(doctor.title, doctor.name, doctor.lastname)
                .joinToString(" ")
                .takeIf { it.isNotBlank() }
        }
        
        val facility = event.clinic?.let { clinic ->
            "${clinic.name}, ${clinic.address}, ${clinic.city}".takeIf { it.isNotBlank() }
        }
        
        val visitType = when (LuxMedEventTypeRemote.fromId(event.eventType)) {
            LuxMedEventTypeRemote.TELEMEDICINE_VISIT -> "Telemedicine"
            LuxMedEventTypeRemote.PHONE_CONSULTATION -> "Phone consultation"
            LuxMedEventTypeRemote.EXAMINATION -> "Examination"
            else -> "In-person visit"
        }
        
        return Visit(
            userId = userId,
            externalId = event.eventId.toString(),
            sourceSystem = SourceSystem.LUXMED,
            visitDate = visitDate,
            doctorName = doctorName,
            specialization = null, // Would need to be fetched from event details
            facility = facility,
            reason = event.title.takeIf { it.isNotBlank() },
            recommendations = null, // Would need to be fetched from event details
            status = mapVisitStatus(event.status),
            visitType = visitType,
            notes = null
        )
    }
    
    private fun parseDateTime(dateString: String): LocalDateTime? {
        return try {
            LocalDateTime.parse(dateString, dateTimeFormatter)
        } catch (e: Exception) {
            logger.warn { "Failed to parse date: $dateString" }
            null
        }
    }
    
    private fun mapEventStatus(statusCode: Int): String {
        return when (statusCode) {
            1 -> "Completed"
            2 -> "Scheduled"
            3 -> "Cancelled"
            4 -> "In Progress"
            5 -> "No Show"
            else -> "Unknown"
        }
    }
    
    private fun mapVisitStatus(statusCode: Int): VisitStatus {
        return when (statusCode) {
            1 -> VisitStatus.COMPLETED
            2 -> VisitStatus.SCHEDULED
            3 -> VisitStatus.CANCELLED
            4 -> VisitStatus.IN_PROGRESS
            5 -> VisitStatus.NO_SHOW
            else -> VisitStatus.UNKNOWN
        }
    }
}
