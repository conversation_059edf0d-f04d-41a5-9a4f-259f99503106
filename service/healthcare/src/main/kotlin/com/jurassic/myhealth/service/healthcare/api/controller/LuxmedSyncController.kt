package com.jurassic.myhealth.service.healthcare.api.controller

import com.jurassic.myhealth.service.auth.domain.model.AuthenticatedUserPrincipal
import com.jurassic.myhealth.service.healthcare.api.mapper.LuxMedAuthenticationMapper
import com.jurassic.myhealth.service.healthcare.api.model.LuxmedSyncRequest
import com.jurassic.myhealth.service.healthcare.api.model.SynchronizationResponse
import com.jurassic.myhealth.service.healthcare.domain.model.UserId
import com.jurassic.myhealth.service.healthcare.domain.usecase.LuxmedDataSynchronizationUseCase
import jakarta.validation.Valid
import mu.KotlinLogging
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

private val logger = KotlinLogging.logger {}

@RestController
@RequestMapping("/api/v1/sync")
class LuxmedSyncController(
    private val luxmedDataSynchronizationUseCase: LuxmedDataSynchronizationUseCase,
    private val luxMedAuthenticationMapper: LuxMedAuthenticationMapper,
) {

    @PostMapping("/luxmed")
    suspend fun syncLuxmedData(
        authentication: AuthenticatedUserPrincipal?,
        @Valid @RequestBody request: LuxmedSyncRequest
    ): ResponseEntity<SynchronizationResponse> {
        return try {
            if (authentication == null) {
                logger.warn { "Authentication failed for LuxMed synchronization request" }
                val response = SynchronizationResponse(
                    success = false,
                    message = "Authentication required for LuxMed synchronization"
                )
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response)
            }

            val luxmedAuthentication = try {
                luxMedAuthenticationMapper.toDomain(request)
            } catch (e: IllegalArgumentException) {
                logger.warn { "Invalid LuxMed authentication components provided: ${e.message}" }
                val response = SynchronizationResponse(
                    success = false,
                    message = e.message ?: "Invalid LuxMed authentication components"
                )
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response)
            }

            logger.info { "Starting LuxMed synchronization for user: ${authentication.internalUserId}" }

            val userId = UserId(authentication.internalUserId)
            val result = luxmedDataSynchronizationUseCase.execute(userId, luxmedAuthentication)

            if (result.isSuccess) {
                logger.info { "LuxMed synchronization completed successfully for user: ${authentication.internalUserId}" }

                val response = SynchronizationResponse(
                    success = true,
                    message = "LuxMed data synchronized successfully"
                )

                ResponseEntity.ok(response)
            } else {
                val error = result.exceptionOrNull()
                logger.error(error) { "LuxMed synchronization failed for user: ${authentication.internalUserId}" }

                val response = SynchronizationResponse(
                    success = false,
                    message = "Failed to synchronize LuxMed data: ${error?.message ?: "Unknown error"}"
                )

                ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response)
            }

        } catch (e: Exception) {
            val userId = authentication?.internalUserId ?: "unknown"
            logger.error(e) { "Unexpected error during LuxMed synchronization for user: $userId" }

            val response = SynchronizationResponse(
                success = false,
                message = "Unexpected error occurred during synchronization"
            )

            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response)
        }
    }
}
