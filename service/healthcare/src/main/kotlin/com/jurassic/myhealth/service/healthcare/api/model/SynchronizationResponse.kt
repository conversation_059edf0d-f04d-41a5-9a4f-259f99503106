package com.jurassic.myhealth.service.healthcare.api.model

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * Simple response DTO for synchronization operations
 */
data class SynchronizationResponse(
    /**
     * Whether the synchronization was successful
     */
    @JsonProperty("success")
    val success: <PERSON><PERSON><PERSON>,
    
    /**
     * Human-readable message about the synchronization
     */
    @JsonProperty("message")
    val message: String
)
