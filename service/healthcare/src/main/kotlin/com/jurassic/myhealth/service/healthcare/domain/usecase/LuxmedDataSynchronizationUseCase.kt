package com.jurassic.myhealth.service.healthcare.domain.usecase

import com.jurassic.myhealth.service.auth.util.andThen
import com.jurassic.myhealth.service.healthcare.domain.gateway.LuxmedMedGateway
import com.jurassic.myhealth.service.healthcare.domain.model.luxmed.LuxMedAuthentication
import com.jurassic.myhealth.service.healthcare.domain.model.UserId
import com.jurassic.myhealth.service.healthcare.domain.repository.LabResultRepository
import com.jurassic.myhealth.service.healthcare.domain.repository.VisitRepository
import mu.KotlinLogging
import org.springframework.stereotype.Service

private val logger = KotlinLogging.logger {}

@Service
class LuxmedDataSynchronizationUseCase(
    private val luxmedMedGateway: LuxmedMedGateway,
    private val labResultRepository: LabResultRepository,
    private val visitRepository: VisitRepository
) {

    suspend fun execute(userId: UserId, luxmedAuthentication: LuxMedAuthentication): Result<Unit> =
            luxmedMedGateway.fetchAllResults(userId, luxmedAuthentication)
                .andThen { medicalData ->
                    runCatching {
                        medicalData.labResults
                            .forEach { labResult -> labResultRepository.save(labResult) }

                        medicalData.visits
                            .forEach { visit -> visitRepository.save(visit) }

                        logger.info {
                            "LuxMed data synchronization completed for user: $userId. " +
                                    "Processed ${medicalData.labResults.size} lab results and ${medicalData.visits.size} visits"
                        }
                    }
                }
    }
