package com.jurassic.myhealth.service.healthcare.domain.repository

import com.jurassic.myhealth.service.healthcare.domain.model.LabResult
import com.jurassic.myhealth.service.healthcare.domain.model.SourceSystem
import com.jurassic.myhealth.service.healthcare.domain.model.UserId
import kotlinx.coroutines.flow.Flow

/**
 * Repository interface for lab results
 */
interface LabResultRepository {

    /**
     * Save a lab result
     */
    suspend fun save(labResult: LabResult): LabResult

    /**
     * Save multiple lab results
     */
    suspend fun saveAll(labResults: List<LabResult>): List<LabResult>

    /**
     * Find lab results by user ID
     */
    fun findByUserId(userId: UserId): Flow<LabResult>

    /**
     * Find a lab result by user ID, source system, and external ID
     */
    suspend fun findByUserIdAndSourceSystemAndExternalId(
        userId: UserId,
        sourceSystem: SourceSystem,
        externalId: String
    ): LabResult?

    /**
     * Check if a lab result exists by user ID, source system, and external ID
     */
    suspend fun existsByUserIdAndSourceSystemAndExternalId(
        userId: UserId,
        sourceSystem: SourceSystem,
        externalId: String
    ): Boolean

    /**
     * Delete lab results by user ID and source system
     */
    suspend fun deleteByUserIdAndSourceSystem(userId: UserId, sourceSystem: SourceSystem)
}
