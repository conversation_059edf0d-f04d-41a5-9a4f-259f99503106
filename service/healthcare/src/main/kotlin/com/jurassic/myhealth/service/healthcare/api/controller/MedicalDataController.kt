package com.jurassic.myhealth.service.healthcare.api.controller

import com.jurassic.myhealth.service.auth.domain.model.AuthenticatedUserPrincipal
import com.jurassic.myhealth.service.healthcare.api.model.LabResultDto
import com.jurassic.myhealth.service.healthcare.api.model.VisitDto
import com.jurassic.myhealth.service.healthcare.api.mapper.LabResultDtoMapper
import com.jurassic.myhealth.service.healthcare.api.mapper.VisitDtoMapper
import com.jurassic.myhealth.service.healthcare.domain.model.UserId
import com.jurassic.myhealth.service.healthcare.domain.usecase.UserMedicalDataRetrievalUseCase
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import mu.KotlinLogging
import org.springframework.web.bind.annotation.*

private val logger = KotlinLogging.logger {}

/**
 * Controller for retrieving medical data
 */
@RestController
@RequestMapping("/api/v1/data")
class MedicalDataController(
    private val userMedicalDataRetrievalUseCase: UserMedicalDataRetrievalUseCase,
    private val labResultDtoMapper: LabResultDtoMapper,
    private val visitDtoMapper: VisitDtoMapper
) {

    /**
     * Get all lab results for the authenticated user
     *
     * @param authentication User authentication information
     * @return Flow of lab results
     */
    @GetMapping("/lab-results")
    fun getLabResults(
        authentication: AuthenticatedUserPrincipal
    ): Flow<LabResultDto> {
        logger.debug { "Retrieving lab results for user: ${authentication.internalUserId}" }

        val userId = UserId(authentication.internalUserId)

        return userMedicalDataRetrievalUseCase.getLabResults(userId)
            .map { labResultDtoMapper.toDto(it) }
    }

    /**
     * Get all visits for the authenticated user
     *
     * @param authentication User authentication information
     * @return Flow of visits
     */
    @GetMapping("/visits")
    fun getVisits(
        authentication: AuthenticatedUserPrincipal
    ): Flow<VisitDto> {
        logger.debug { "Retrieving visits for user: ${authentication.internalUserId}" }

        val userId = UserId(authentication.internalUserId)

        return userMedicalDataRetrievalUseCase.getVisits(userId)
            .map { visitDtoMapper.toDto(it) }
    }

    /**
     * Get all medical data for the authenticated user
     *
     * @param authentication User authentication information
     * @return Medical data summary
     */
    @GetMapping("/all")
    suspend fun getAllMedicalData(
        authentication: AuthenticatedUserPrincipal
    ): MedicalDataSummary {
        logger.debug { "Retrieving all medical data for user: ${authentication.internalUserId}" }

        val userId = UserId(authentication.internalUserId)

        val medicalData = userMedicalDataRetrievalUseCase.getAllMedicalData(userId)

        return MedicalDataSummary(
            labResults = labResultDtoMapper.toDtoList(medicalData.labResults),
            visits = visitDtoMapper.toDtoList(medicalData.visits)
        )
    }
}

/**
 * Summary of all medical data for a user
 */
data class MedicalDataSummary(
    val labResults: List<LabResultDto>,
    val visits: List<VisitDto>
)
