package com.jurassic.myhealth.service.healthcare.data.client.luxmed

import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.timeline.EventDetailsRequest
import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.timeline.EventDetailsResponse
import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.timeline.TimelineRequest
import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.timeline.TimelineResponse
import com.jurassic.myhealth.service.healthcare.domain.model.luxmed.LuxMedAuthentication

/**
 * Client interface for the LuxMed API
 *
 * Authentication requires LuxMedAuthentication domain object with all essential components:
 * - JWT token for authorization-token header
 * - ASP.NET session ID, LX token, refresh token, user additional info, XSRF token cookies
 * - Optional: Incapsula session and device ID
 */
interface LuxMedClient {
    /**
     * Get timeline events
     * @param authentication LuxMed authentication context with all required components
     * @param request Timeline request parameters
     * @return Timeline response with events
     */
    suspend fun getTimelineEvents(authentication: LuxMedAuthentication, request: TimelineRequest): TimelineResponse

    /**
     * Get event details
     * @param authentication LuxMed authentication context with all required components
     * @param request Event details request parameters
     * @return Event details response
     */
    suspend fun getEventDetails(authentication: LuxMedAuthentication, request: EventDetailsRequest): EventDetailsResponse
}
