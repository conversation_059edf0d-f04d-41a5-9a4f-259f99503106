package com.jurassic.myhealth.service.api

import com.jurassic.myhealth.service.auth.domain.model.AuthenticatedUserPrincipal
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.Authentication
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import kotlin.collections.mapOf

/**
 * Sample controller to demonstrate how to use authentication.
 */
@RestController
@RequestMapping("/api/v1/sample")
class SampleController {

    /**
     * Public endpoint that doesn't require authentication.
     */
    @GetMapping("/public")
    fun publicEndpoint(): ResponseEntity<Map<String, String>> {
        return ResponseEntity.ok(mapOf("message" to "This is a public endpoint"))
    }

    /**
     * Protected endpoint that requires authentication.
     */
    @GetMapping("/protected")
    @PreAuthorize("isAuthenticated()")
    fun protectedEndpoint(authentication: Authentication): ResponseEntity<Map<String, Any>> {
        val principal = authentication.principal as AuthenticatedUserPrincipal

        return ResponseEntity.ok(
            mapOf(
                "message" to "This is a protected endpoint",
                "user" to mapOf(
                    "id" to principal.internalUserId,
                )
            )
        )
    }

    /**
     * Admin endpoint that requires ADMIN role.
     */
    @GetMapping("/admin")
    @PreAuthorize("hasRole('ADMIN')")
    fun adminEndpoint(): ResponseEntity<Map<String, String>> {
        return ResponseEntity.ok(mapOf("message" to "This is an admin endpoint"))
    }
}