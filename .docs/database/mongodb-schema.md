# MyHealth Service - MongoDB Data Model

This document describes the MongoDB schema and data model for the MyHealth Service application.

## Database Configuration

- **Database Name**: `myhealth`
- **Connection**: `mongodb://localhost:27017`
- **Container**: `myhealth-mongodb` (MongoDB 7.0)
- **Driver**: Spring Data Reactive MongoDB

## Collections Overview

### 🔐 users Collection
Stores user authentication and profile information from external identity providers.

**Collection Name**: `users`

| Field | Type | Description |
|-------|------|-------------|
| `_id` | String (ObjectId) | Primary key |
| `externalId` | String | External provider ID |
| `identityProvider` | String | Auth provider (Firebase, etc.) |
| `email` | String | User email address |
| `firstName` | String | User's first name |
| `lastName` | String | User's last name |
| `createdAt` | Instant | Record creation timestamp |
| `updatedAt` | Instant | Last update timestamp |

**Indexes**:
- Compound index on `externalId + identityProvider` (unique)

### 🧪 lab_results Collection
Stores laboratory test results from external healthcare systems.

**Collection Name**: `lab_results`

| Field | Type | Description |
|-------|------|-------------|
| `_id` | String (ObjectId) | Primary key |
| `userId` | String | Foreign key to users._id |
| `externalId` | String | External system identifier |
| `sourceSystem` | String | Source system (LUXMED, MEDICOVER, etc.) |
| `testName` | String | Name of the laboratory test |
| `parameters` | Array[LabParameterDocument] | List of test parameters |
| `observationDate` | LocalDateTime | When test was performed |
| `status` | String | Test status |
| `reportUrl` | String? | Optional PDF report URL |
| `notes` | String? | Additional notes |
| `createdAt` | LocalDateTime | Record creation timestamp |
| `updatedAt` | LocalDateTime | Last update timestamp |

**Indexes**:
- Single index on `userId`
- Single index on `sourceSystem`
- Single index on `createdAt`
- Compound index on `userId + sourceSystem + externalId` (unique)

### 🏥 visits Collection
Stores medical visit information from external healthcare systems.

**Collection Name**: `visits`

| Field | Type | Description |
|-------|------|-------------|
| `_id` | String (ObjectId) | Primary key |
| `userId` | String | Foreign key to users._id |
| `externalId` | String | External system identifier |
| `sourceSystem` | String | Source system (LUXMED, MEDICOVER, etc.) |
| `visitDate` | LocalDateTime | Date of medical visit |
| `doctorName` | String? | Doctor's name |
| `specialization` | String? | Medical specialization |
| `facility` | String? | Medical facility name |
| `reason` | String? | Visit reason |
| `recommendations` | String? | Doctor recommendations |
| `status` | String | Visit status |
| `visitType` | String? | Visit type (in-person, telemedicine, phone) |
| `notes` | String? | Additional notes |
| `createdAt` | LocalDateTime | Record creation timestamp |
| `updatedAt` | LocalDateTime | Last update timestamp |

**Indexes**:
- Single index on `userId`
- Single index on `sourceSystem`
- Single index on `visitDate`
- Single index on `createdAt`
- Compound index on `userId + sourceSystem + externalId` (unique)

### 📊 LabParameterDocument (Embedded)
Embedded document within lab_results representing individual test parameters.

| Field | Type | Description |
|-------|------|-------------|
| `name` | String | Parameter name (e.g., "Hemoglobin", "Glucose") |
| `value` | String | Measured value |
| `unit` | String? | Unit of measurement (e.g., "g/dL", "mg/dL") |
| `referenceRange` | String? | Normal range (e.g., "12.0-15.5") |
| `interpretation` | String? | Result interpretation (Normal, High, Low, etc.) |
| `notes` | String? | Additional notes |

## Relationships

```
users (1) ──── (many) lab_results
users (1) ──── (many) visits
lab_results (1) ──── (many) lab_parameters (embedded)
```

## Data Flow

1. **User Authentication**: Users are created/retrieved from external identity providers
2. **Data Synchronization**: Medical data is fetched from external systems (LuxMed, Medicover)
3. **Deduplication**: Unique constraints prevent duplicate records from the same source
4. **Retrieval**: Medical data is queried by userId for user-specific views

## Key Features

✅ **Multi-Source Support**: Designed to handle data from multiple healthcare providers  
✅ **Deduplication**: Unique constraints prevent duplicate data from external systems  
✅ **Optimized Queries**: Indexes support efficient user-based and date-based queries  
✅ **Audit Trail**: Created/updated timestamps for all records  
✅ **Flexible Schema**: Optional fields accommodate varying data from different sources  
✅ **Embedded Documents**: Lab parameters are embedded for atomic operations  

## Repository Patterns

- **Auth Module**: Traditional MongoDB repositories for user management
- **Healthcare Module**: Reactive MongoDB repositories with Kotlin Flow support
- **Upsert Operations**: MongoDB upsert used for data synchronization to handle duplicates

## Configuration Classes

- `MongoConfig`: Shared MongoDB configuration for all modules
- `UserRepositoryImpl`: Auth module repository implementation
- `LabResultRepositoryImpl`: Healthcare module lab results repository
- `VisitRepositoryImpl`: Healthcare module visits repository
