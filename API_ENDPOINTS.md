# Healthcare API Endpoints

## Synchronization

### Sync LuxMed Data
```http
POST /api/v1/sync/luxmed
Content-Type: application/json
Authorization: Bearer <token>

{
  "jwtToken": "jwt_token_from_authorization_header",
  "aspNetSessionId": "session_id_from_cookie",
  "lxToken": "lx_token_from_cookie",
  "refreshToken": "refresh_token_from_cookie",
  "userAdditionalInfo": "user_info_jwt_from_cookie",
  "xsrfToken": "xsrf_token_from_cookie",
  "incapsulaSessionId": "optional_incapsula_session",
  "deviceId": "optional_device_id"
}
```

**Response:**
```json
{
  "success": true,
  "message": "LuxMed data synchronized successfully"
}
```

## Data Retrieval

### Get Lab Results
```http
GET /api/v1/data/lab-results
Authorization: Bearer <token>
```

### Get Visits
```http
GET /api/v1/data/visits
Authorization: Bearer <token>
```

### Get All Medical Data
```http
GET /api/v1/data/all
Authorization: Bearer <token>
```

**Response:**
```json
{
  "labResults": [...],
  "visits": [...]
}
```
